export default defineAppConfig({
	//host: 'https://beta.tvornicazdravehrane.com',
	host: 'https://tzh.markerheadless.info',
	lang: 'hr',
	baseCompatibility: '1.30.0',
	cache: {
		enabled: process.env.NODE_ENV == 'development' ? false : true,
	},
	publish: {
		postsResponseFields: ['id', 'url_without_domain', 'title', 'category_title', 'headline', 'main_image_thumbs', 'main_image_title', 'main_image_description', 'images', 'short_description', 'feedback_comment_widget', 'feedback_rate_widget', 'category_url_without_domain', 'attributes_summary'],
	},
	webshop: {
		steps: ['customer', 'payment', 'reviewOrder'],
	},
	catalog: {
		quickOrder: true,
		productsResponseFields: [
			'id',
			'code',
			'is_available',
			'priority_details',
			'category_code',
			'category_title',
			'discount_percent',
			'discount_percent_custom',
			'payment_prices',
			'price_custom',
			'basic_price',
			'basic_price_custom',
			'price_b2b_custom',
			'basic_price_b2b_custom',
			'loyalty_price',
			'rate',
			'price_rate_custom',
			'url_without_domain',
			'main_image_thumbs',
			'main_image_title',
			'main_image_description',
			'title',
			'is_available',
			'attributes_special',
			'extra_price_lowest',
			'manufacturer_code',
			'manufacturer_url_without_domain',
			'manufacturer_title',
			'manufacturer_main_image_upload_path',
			'manufacturer_main_image_thumbs',
			'shopping_cart_code',
			'status',
			'feedback_comment_widget',
			'feedback_rate_widget',
			'type',
			'available_qty',
			'package_qty',
			'qty',
			'warehouses',
			'attributeitems_special',
			'attributes_special',
			'images',
			'element_video',
			'seo_h1',
			'category_url_without_domain',
			'limited_qty',
			'category_position_h',
		],
	},
	layouts: [
		{
			template: 'CmsAbout',
			layout: 'about',
		},
	],
	facebook: {
		pixel: {
			env: ['production', 'development'],
			apiKey: '759023024186954',
			target: 'head',
		},
	},
	google: {
		gtm: {
			env: ['production', 'development'],
			gdpr: false,
		},
		tracking: {
			gdpr: false,
			debug: false,
			events: ['gdprConsents', 'viewItem', 'selectItem', 'viewItemList', 'addToWishlist', 'removeProduct', 'addProduct', 'viewCart', 'beginCheckout', 'addShippingInfo', 'addPaymentInfo', 'purchase', 'refund', 'viewPromotion', 'selectPromotion'],
			hooks: {
				mapProduct(product, gtmData) {
					return {
						...gtmData,
						item_discount_percentage: product.discount_percent_custom || 0,
						item_reviews_rating: product.feedback_rate_widget?.rates ? Math.round(product.feedback_rate_widget.rates) : 0,
						item_reviews_no: product.feedback_rate_widget?.rates_votes ? Number(product.feedback_rate_widget.rates_votes) : 0,
					};
				},
			},
			viewCart: {
				hooks: {
					mapData(data) {
						const res = data;
						delete res.ecommerce.value;
						delete res.ecommerce.currency;
						return res;
					},
				},
			},
			addShippingInfo: {
				hooks: {
					mapData(data) {
						const res = data;
						delete res.ecommerce.value;
						delete res.ecommerce.currency;
						delete res.ecommerce.coupon;
						return res;
					},
				},
			},
			addPaymentInfo: {
				hooks: {
					mapData(data) {
						const res = data;
						delete res.ecommerce.value;
						delete res.ecommerce.currency;
						delete res.ecommerce.coupon;
						return res;
					},
				},
			},
		},
		remarketing: {
			env: ['production', 'development'],
			debug: true,
			events: ['home', 'category', 'offerdetail', 'searchresults', 'conversionintent', 'conversion'],
			gdpr: false,
		},
	},
});
