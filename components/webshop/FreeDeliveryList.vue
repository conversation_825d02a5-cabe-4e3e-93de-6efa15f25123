<template>
	<BaseCatalogLists :fetch="{code: 'checkout_suggestion', use_total: true, cache_lifetime: 60, response_fields: ['code', 'url_without_domain']}" v-slot="{items: list}">
		<BaseCatalogProductsWidget
			:fetch="{list_code: list[0].code, sort: 'list_position', only_available: true, limit: 12, category_id: cart.product_categories, id_exclude: productIds, exclude_new: true}"
			v-slot="{items: products}"
			v-if="list?.length && toFree?.amount > 0 && cart?.product_categories?.length"
			:gtm-tracking="{item_list_id: 'checkout_suggestion', item_list_name: labels.get('free_delivery_widget_title')}">
			<div class="cart_info_total_extra_shipping_to_free_box free-delivery-widget active" :class="[mode]">
				<div class="fdw-title">
					<BaseCmsLabel code="free_delivery_widget_title" />
					<NuxtLink class="btn" :to="list[0].url_without_domain"><BaseCmsLabel code="show_all" tag="span" /></NuxtLink>
				</div>
				<div class="fdw-note" v-html="labels.get('free_delivery_widget_note').replace('%FREE_ABOVE%', formatCurrency(toFree.above))"></div>

				<div class="fdw-container">
					<div class="fwd-slider slick-carousel slick-arrow3 blazy-container">
						<CatalogSpecialLists :list="list[0]" :items="products" :perPage="3" />
					</div>
				</div>
			</div>
		</BaseCatalogProductsWidget>
	</BaseCatalogLists>
</template>

<script setup>
	const labels = useLabels();
	const {formatCurrency} = useCurrency();
	const props = defineProps(['mode', 'cart', 'toFree', 'parcels']);

	const {getCartData} = useWebshop();
	const cartData = getCartData('products');
	const productIds = cartData.map(item => item.id);
</script>
